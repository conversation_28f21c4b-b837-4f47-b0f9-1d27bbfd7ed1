import { PrismaService } from 'nestjs-prisma';
export declare class ChatbotService {
    private prisma;
    private readonly logger;
    private readonly groqApiKey;
    private readonly groqModel;
    constructor(prisma: PrismaService);
    processMessage(message: string): Promise<string>;
    private isGreeting;
    private isHelpRequest;
    private isStatsRequest;
    private getGeneralStats;
    private handleEntityQueries;
    private isCountQuery;
    private isListQuery;
    private isDetailQuery;
    private askGroq;
    executeQuery(query: string): Promise<any>;
}
